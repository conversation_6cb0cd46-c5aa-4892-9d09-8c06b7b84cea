/**
 * Application Configuration Constants
 * 
 * This file contains all application-wide constants and configuration values.
 * Import these constants instead of hardcoding values throughout the application.
 */

// Contact Management Constants
export const MAX_CONTACTS_LIMIT = 4;

// Image Configuration Constants
export const MAX_IMAGES_LIMIT = 3;
export const IMAGE_RESIZE_WIDTH = 300;

// Default URLs and Assets
export const DEFAULT_AVATAR_URL = "https://raw.githubusercontent.com/mantinedev/mantine/master/.demo/avatars/avatar-2.png";

// API Configuration
export const DOMAIN_API_BASE_URL = "https://api.odude.com/opensea.php";

// Notification Configuration
export const NOTIFICATION_POSITION = "bottom-right";
export const NOTIFICATION_SIZE = "sm";

// UI Configuration
export const SOCIAL_ICON_SIZE = 60;
export const SOCIAL_ICON_RADIUS = "xl";

// Validation Constants
export const MAX_ODUDE_NAME_LENGTH = 253;
export const MAX_ODUDE_NAME_SEGMENT_LENGTH = 63;

//View profile URL
export const VIEW_PROFILE_URL = "https://name.odude.com/profile/";

// Social Media Configuration
export const SOCIAL_SLOTS = ["twitter", "telegram", "youtube", "instagram", "facebook", "discord"];

// Cryptocurrency Configuration
export const CRYPTO_SLOTS = ["eth", "bsc", "matic", "btc", "fil", "sol"];

// Notes Configuration
export const MAX_NOTES_CHARACTER_LIMIT = 100;

// Name Domain Configuration
export const NAME_SLOTS = ["me", "school", "shop", "cafe"];

// SMTP Configuration for Contact Us
export const SMTP_CONFIG = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: true, // true for 465, false for other ports
  auth: {
    user: process.env.SMTP_USER || '',
    pass: process.env.SMTP_PASS || ''
  }
};

// Contact Us Configuration
export const CONTACT_EMAIL = process.env.CONTACT_EMAIL || '<EMAIL>';

// AdSense Configuration
export const ENABLE_ADSENSE = process.env.NEXT_PUBLIC_ENABLE_ADSENSE === 'true';
export const ADSENSE_CLIENT_ID = process.env.NEXT_PUBLIC_ADSENSE_CLIENT_ID || '';
export const ADSENSE_CONFIG = {
  // Style 1: Large Banner (728x90)
  style1: {
    slot: process.env.NEXT_PUBLIC_ADSENSE_SLOT_1 || '',
    width: 728,
    height: 90,
    format: 'auto',
    responsive: true
  },
  // Style 2: Medium Rectangle (300x250)
  style2: {
    slot: process.env.NEXT_PUBLIC_ADSENSE_SLOT_2 || '',
    width: 300,
    height: 250,
    format: 'auto',
    responsive: true
  },
  // Style 3: Large Rectangle (336x280)
  style3: {
    slot: process.env.NEXT_PUBLIC_ADSENSE_SLOT_3 || '',
    width: 336,
    height: 280,
    format: 'auto',
    responsive: true
  }
};
