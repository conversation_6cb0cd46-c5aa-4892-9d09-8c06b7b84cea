'use client';

import { useEffect } from 'react';
import { Box } from '@mantine/core';
import { ENABLE_ADSENSE, ADSENSE_CLIENT_ID, ADSENSE_CONFIG } from 'src/lib/config';

interface AdSenseBannerProps {
  style: 1 | 2 | 3;
  className?: string;
}

declare global {
  interface Window {
    adsbygoogle: any[];
  }
}

export function AdSenseBanner({ style, className }: AdSenseBannerProps) {
  // Don't render if AdSense is disabled or client ID is not set
  if (!ENABLE_ADSENSE || !ADSENSE_CLIENT_ID) {
    return null;
  }

  const config = ADSENSE_CONFIG[`style${style}`];
  
  // Don't render if slot is not configured
  if (!config.slot) {
    return null;
  }

  useEffect(() => {
    try {
      // Initialize adsbygoogle array if it doesn't exist
      if (typeof window !== 'undefined') {
        window.adsbygoogle = window.adsbygoogle || [];
        window.adsbygoogle.push({});
      }
    } catch (error) {
      console.error('AdSense error:', error);
    }
  }, []);

  return (
    <Box className={className} style={{ textAlign: 'center', margin: '20px 0' }}>
      <ins
        className="adsbygoogle"
        style={{
          display: 'inline-block',
          width: config.responsive ? '100%' : `${config.width}px`,
          height: `${config.height}px`,
          maxWidth: `${config.width}px`
        }}
        data-ad-client={ADSENSE_CLIENT_ID}
        data-ad-slot={config.slot}
        data-ad-format={config.format}
        data-full-width-responsive={config.responsive ? 'true' : 'false'}
      />
    </Box>
  );
}
